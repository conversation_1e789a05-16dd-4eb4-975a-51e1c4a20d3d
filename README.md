# Crypto Challenge: Multi-Layer Cipher

## Challenge Description

You've intercepted an encrypted message that uses multiple layers of encryption. The message contains a flag in the format `TRIADA{...}`. Your task is to decrypt it by understanding and reversing each layer of the encryption process.

## Difficulty: Medium-Hard

This challenge combines several cryptographic concepts:
- Classical ciphers (Caesar/ROT13)
- XOR encryption with key
- Matrix-based transformations
- Base64 encoding
- Reverse engineering multi-layer encryption

## Files Provided

- `crypto_challenge.txt` - Contains the encrypted flag and all necessary hints
- This README file

## What You Need to Know

The encryption process involves 4 layers that must be reversed in the correct order. The hints in the challenge file provide crucial information about each layer.

### Skills Required:
- Classical cryptography (Caesar cipher)
- XOR operations and key recovery
- Linear algebra (matrix operations and inverses)
- Base64 encoding/decoding
- Python programming
- Understanding of modular arithmetic

### Layer Analysis:
1. **Base64 Encoding** - Common encoding scheme (outermost layer)
2. **Matrix Transformation** - 2x2 matrix with determinant 13
3. **XOR Cipher** - 10-character key related to challenge theme
4. **Caesar Cipher** - Classic ROT13 variant (innermost layer)

## Approach Strategy

1. **Start from the outside** - Base64 decode the encrypted flag
2. **Reverse the matrix** - Calculate the inverse of the 2x2 matrix mod 256
3. **Break the XOR** - Find the 10-character key (hint: theme + year)
4. **Solve the Caesar** - Apply reverse ROT13 transformation
5. **Verify the flag** - Should be readable and follow TRIADA{...} format

## Mathematical Notes

- Matrix determinant is 13 (coprime to 256, so inverse exists)
- XOR key is exactly 10 characters long
- Caesar shift is 13 (ROT13)
- All operations use modular arithmetic

## Tools You Might Need

- Python with base64 library
- Understanding of matrix inverse calculations
- XOR operations
- String manipulation

## Flag Format

The flag follows the format: `TRIADA{...}` and contains leetspeak/text speak.

## Verification

Once you think you have the flag, make sure it:
- Starts with `TRIADA{`
- Ends with `}`
- Contains readable text (may include numbers/leetspeak)
- Makes sense in the context of layered cryptography

## Hints Breakdown

- **Hint 1**: 4 layers total - work backwards
- **Hint 2**: ROT13 is the innermost layer
- **Hint 3**: XOR key = "CRYPTO2024" (theme + year)
- **Hint 4**: Matrix [[3,2],[1,5]] with det=13
- **Hint 5**: Base64 is the outermost layer
- **Hint 6**: Reverse the process step by step

Good luck! This challenge tests your understanding of multiple cryptographic techniques and how they can be layered together.

---

**Note for CTF Organizers**: The solution script is provided separately for verification purposes. Expected solve time: 20-45 minutes for experienced participants.
