#!/usr/bin/env python3
"""
Simplified Crypto Challenge Generator
Creates a multi-layer encryption that's challenging but solvable
"""

import base64
import hashlib
from Crypto.Util.number import bytes_to_long, long_to_bytes

def xor_cipher(data, key):
    """XOR cipher with repeating key"""
    if isinstance(data, str):
        data = data.encode()
    if isinstance(key, str):
        key = key.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def caesar_cipher(text, shift):
    """Caesar cipher for alphabetic characters"""
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset + shift) % 26 + ascii_offset)
        else:
            result += char
    return result

def matrix_transform(data, key_matrix):
    """Simple matrix transformation"""
    # Convert to numbers
    nums = [ord(c) if isinstance(c, str) else c for c in data]
    
    # Pad to make divisible by matrix size
    while len(nums) % len(key_matrix) != 0:
        nums.append(0)
    
    result = []
    for i in range(0, len(nums), len(key_matrix)):
        chunk = nums[i:i+len(key_matrix)]
        transformed_chunk = []
        for j in range(len(key_matrix)):
            val = 0
            for k in range(len(key_matrix)):
                val += key_matrix[j][k] * chunk[k]
            transformed_chunk.append(val % 256)
        result.extend(transformed_chunk)
    
    return bytes(result)

def reverse_matrix_transform(data, key_matrix):
    """Reverse the matrix transformation"""
    # Calculate matrix inverse mod 256
    def matrix_inverse_2x2(matrix):
        a, b = matrix[0]
        c, d = matrix[1]
        det = (a * d - b * c) % 256
        
        # Find modular inverse of determinant
        det_inv = pow(det, -1, 256)
        
        inv_matrix = [
            [(d * det_inv) % 256, (-b * det_inv) % 256],
            [(-c * det_inv) % 256, (a * det_inv) % 256]
        ]
        return inv_matrix
    
    inv_matrix = matrix_inverse_2x2(key_matrix)
    
    nums = list(data)
    result = []
    
    for i in range(0, len(nums), 2):
        chunk = nums[i:i+2]
        if len(chunk) < 2:
            chunk.append(0)
        
        decrypted_chunk = []
        for j in range(2):
            val = 0
            for k in range(2):
                val += inv_matrix[j][k] * chunk[k]
            decrypted_chunk.append(val % 256)
        result.extend(decrypted_chunk)
    
    # Remove padding
    while result and result[-1] == 0:
        result.pop()
    
    return bytes(result)

def main():
    flag = "TRIADA{l4y3r3d_cryp70_1s_fun_bu7_n07_1mp0ss1bl3}"
    
    print("Generating simplified crypto challenge...")
    
    # Layer 1: Caesar cipher (shift 13 - ROT13 variant)
    layer1 = caesar_cipher(flag, 13)
    print(f"After Caesar: {layer1}")
    
    # Layer 2: XOR with key
    xor_key = "CRYPTO2024"
    layer2 = xor_cipher(layer1, xor_key)
    print(f"After XOR: {layer2.hex()}")
    
    # Layer 3: Matrix transformation (2x2 for simplicity)
    matrix_key = [[3, 2], [1, 5]]  # det = 3*5 - 2*1 = 13 (coprime to 256)
    layer3 = matrix_transform(layer2, matrix_key)
    print(f"After Matrix: {layer3.hex()}")
    
    # Layer 4: Base64 encoding
    layer4 = base64.b64encode(layer3).decode()
    print(f"Final encoded: {layer4}")
    
    # Create challenge file
    with open('crypto_challenge.txt', 'w') as f:
        f.write("=== TRIADA CRYPTO CHALLENGE: Multi-Layer Cipher ===\n\n")
        f.write(f"Encrypted Flag: {layer4}\n\n")
        f.write("=== HINTS ===\n")
        f.write("Hint 1: The encryption uses 4 layers in sequence\n")
        f.write("Hint 2: One layer is a classic substitution cipher with shift 13\n")
        f.write("Hint 3: XOR key is related to the challenge theme and year\n")
        f.write("Hint 4: Matrix is 2x2 with small positive integers, determinant = 13\n")
        f.write("Hint 5: Final layer is a common encoding scheme\n")
        f.write("Hint 6: Work backwards from the final result\n\n")
        f.write("=== ADDITIONAL INFO ===\n")
        f.write("Matrix determinant: 13\n")
        f.write("XOR key length: 10 characters\n")
        f.write("Caesar shift: Classic ROT variant\n")
        f.write("Layers: Base64 -> Matrix -> XOR -> Caesar -> Original\n")
    
    # Create solution
    with open('crypto_solution.py', 'w') as f:
        f.write(f'''#!/usr/bin/env python3
"""
Solution for Multi-Layer Cipher Challenge
"""

import base64

def xor_cipher(data, key):
    """XOR cipher with repeating key"""
    if isinstance(key, str):
        key = key.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def caesar_decipher(text, shift):
    """Reverse Caesar cipher"""
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
        else:
            result += char
    return result

def reverse_matrix_transform(data, key_matrix):
    """Reverse the matrix transformation"""
    # Calculate matrix inverse mod 256
    def matrix_inverse_2x2(matrix):
        a, b = matrix[0]
        c, d = matrix[1]
        det = (a * d - b * c) % 256
        
        # Find modular inverse of determinant
        det_inv = pow(det, -1, 256)
        
        inv_matrix = [
            [(d * det_inv) % 256, (-b * det_inv) % 256],
            [(-c * det_inv) % 256, (a * det_inv) % 256]
        ]
        return inv_matrix
    
    inv_matrix = matrix_inverse_2x2(key_matrix)
    
    nums = list(data)
    result = []
    
    for i in range(0, len(nums), 2):
        chunk = nums[i:i+2]
        if len(chunk) < 2:
            chunk.append(0)
        
        decrypted_chunk = []
        for j in range(2):
            val = 0
            for k in range(2):
                val += inv_matrix[j][k] * chunk[k]
            decrypted_chunk.append(val % 256)
        result.extend(decrypted_chunk)
    
    # Remove padding
    while result and result[-1] == 0:
        result.pop()
    
    return bytes(result)

# Given encrypted flag
encrypted_flag = "{layer4}"

print("Solving Multi-Layer Cipher...")

# Step 1: Base64 decode
step1 = base64.b64decode(encrypted_flag)
print(f"After Base64 decode: {{step1.hex()}}")

# Step 2: Reverse matrix transformation
matrix_key = [[3, 2], [1, 4]]
step2 = reverse_matrix_transform(step1, matrix_key)
print(f"After matrix reverse: {{step2.hex()}}")

# Step 3: Reverse XOR
xor_key = "CRYPTO2024"
step3 = xor_cipher(step2, xor_key)
print(f"After XOR reverse: {{step3}}")

# Step 4: Reverse Caesar cipher (ROT13)
flag = caesar_decipher(step3.decode(), 13)
print(f"Final flag: {{flag}}")
''')
    
    print("\nChallenge files created:")
    print("- crypto_challenge.txt (give this to participants)")
    print("- crypto_solution.py (keep this for verification)")
    print(f"- Flag: {flag}")

if __name__ == "__main__":
    main()
