=== TRIADA CRYPTO CHALLENGE: Multi-Layer Cipher ===

Encrypted Flag: OndppREKbbbCvr6DgA+fRr0j+WBztKR2mTew7/00SyR0+xy8SXKG+8F3fyQdSZ2C

=== HINTS ===
Hint 1: The encryption uses 4 layers in sequence
Hint 2: One layer is a classic substitution cipher with shift 13
Hint 3: XOR key is related to the challenge theme and year
Hint 4: Matrix is 2x2 with small positive integers, determinant = 13
Hint 5: Final layer is a common encoding scheme
Hint 6: Work backwards from the final result

=== ADDITIONAL INFO ===
Matrix determinant: 13
XOR key length: 10 characters
Caesar shift: Classic ROT variant
Layers: Base64 -> Matrix -> XOR -> Caesar -> Original
