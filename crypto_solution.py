#!/usr/bin/env python3
"""
Solution for Multi-Layer Cipher Challenge
"""

import base64

def xor_cipher(data, key):
    """XOR cipher with repeating key"""
    if isinstance(key, str):
        key = key.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def caesar_decipher(text, shift):
    """Reverse Caesar cipher"""
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset - shift) % 26 + ascii_offset)
        else:
            result += char
    return result

def reverse_matrix_transform(data, key_matrix):
    """Reverse the matrix transformation"""
    # Calculate matrix inverse mod 256
    def matrix_inverse_2x2(matrix):
        a, b = matrix[0]
        c, d = matrix[1]
        det = (a * d - b * c) % 256
        
        # Find modular inverse of determinant
        det_inv = pow(det, -1, 256)
        
        inv_matrix = [
            [(d * det_inv) % 256, (-b * det_inv) % 256],
            [(-c * det_inv) % 256, (a * det_inv) % 256]
        ]
        return inv_matrix
    
    inv_matrix = matrix_inverse_2x2(key_matrix)
    
    nums = list(data)
    result = []
    
    for i in range(0, len(nums), 2):
        chunk = nums[i:i+2]
        if len(chunk) < 2:
            chunk.append(0)
        
        decrypted_chunk = []
        for j in range(2):
            val = 0
            for k in range(2):
                val += inv_matrix[j][k] * chunk[k]
            decrypted_chunk.append(val % 256)
        result.extend(decrypted_chunk)
    
    # Remove padding
    while result and result[-1] == 0:
        result.pop()
    
    return bytes(result)

# Given encrypted flag
encrypted_flag = "OndppREKbbbCvr6DgA+fRr0j+WBztKR2mTew7/00SyR0+xy8SXKG+8F3fyQdSZ2C"

print("Solving Multi-Layer Cipher...")

# Step 1: Base64 decode
step1 = base64.b64decode(encrypted_flag)
print(f"After Base64 decode: {step1.hex()}")

# Step 2: Reverse matrix transformation
matrix_key = [[3, 2], [1, 5]]
step2 = reverse_matrix_transform(step1, matrix_key)
print(f"After matrix reverse: {step2.hex()}")

# Step 3: Reverse XOR
xor_key = "CRYPTO2024"
step3 = xor_cipher(step2, xor_key)
print(f"After XOR reverse: {step3}")

# Step 4: Reverse Caesar cipher (ROT13)
flag = caesar_decipher(step3.decode(), 13)
print(f"Final flag: {flag}")
